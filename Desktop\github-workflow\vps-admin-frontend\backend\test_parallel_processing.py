#!/usr/bin/env python3
"""
Test script to verify parallel processing capabilities of the VPS Admin system.
This script simulates multiple concurrent requests to test if the system can handle them properly.
"""

import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_TASKS = [
    {
        "initial_prompt": "Check disk space and memory usage",
        "task_metadata": {
            "title": "System Status Check",
            "description": "Check basic system metrics",
            "priority": "medium"
        }
    },
    {
        "initial_prompt": "List running processes and services",
        "task_metadata": {
            "title": "Process Monitoring",
            "description": "Monitor system processes",
            "priority": "low"
        }
    }
]

class ParallelProcessingTester:
    def __init__(self):
        self.session = None
        self.task_ids: List[str] = []
        self.results: Dict[str, Any] = {}

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def create_task(self, task_config: Dict[str, Any]) -> str:
        """Create a new task and return its ID."""
        print(f"Creating task: {task_config['task_metadata']['title']}")
        
        async with self.session.post(
            f"{BASE_URL}/start_task",
            json=task_config
        ) as response:
            if response.status == 201:
                data = await response.json()
                task_id = data["task_id"]
                print(f"✓ Task created with ID: {task_id}")
                return task_id
            else:
                error_text = await response.text()
                print(f"✗ Failed to create task: {response.status} - {error_text}")
                raise Exception(f"Failed to create task: {response.status}")

    async def send_message_to_task(self, task_id: str, message: str) -> List[Dict[str, Any]]:
        """Send a message to a task and collect all SSE events."""
        print(f"Sending message to {task_id}: '{message}'")
        events = []
        
        try:
            async with self.session.post(
                f"{BASE_URL}/send_message",
                json={"task_id": task_id, "message": message}
            ) as response:
                if response.status == 200:
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                event_data = json.loads(line[6:])  # Remove 'data: ' prefix
                                events.append(event_data)
                                print(f"  📨 {task_id}: {event_data.get('type', 'unknown')} - {event_data.get('content', '')[:100]}...")
                                
                                # Stop if we get an end event
                                if event_data.get('type') == 'task_end':
                                    break
                            except json.JSONDecodeError:
                                continue
                else:
                    error_text = await response.text()
                    print(f"✗ Failed to send message to {task_id}: {response.status} - {error_text}")
                    
        except Exception as e:
            print(f"✗ Error sending message to {task_id}: {e}")
            
        return events

    async def test_concurrent_task_creation(self):
        """Test creating multiple tasks concurrently."""
        print("\n🧪 Testing concurrent task creation...")
        
        # Create tasks concurrently
        tasks = [self.create_task(config) for config in TEST_TASKS]
        self.task_ids = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        valid_task_ids = [tid for tid in self.task_ids if isinstance(tid, str)]
        print(f"✓ Created {len(valid_task_ids)} tasks successfully")
        
        return valid_task_ids

    async def test_concurrent_messaging(self, task_ids: List[str]):
        """Test sending messages to multiple tasks concurrently."""
        print("\n🧪 Testing concurrent messaging...")
        
        # Send initial messages to all tasks concurrently
        message_tasks = [
            self.send_message_to_task(task_id, "Start the task")
            for task_id in task_ids
        ]
        
        # Wait for all messages to be processed
        results = await asyncio.gather(*message_tasks, return_exceptions=True)
        
        print(f"✓ Sent messages to {len(task_ids)} tasks concurrently")
        return results

    async def test_rapid_fire_messages(self, task_id: str):
        """Test sending multiple messages rapidly to the same task."""
        print(f"\n🧪 Testing rapid-fire messages to task {task_id}...")
        
        messages = [
            "Check system status",
            "What's the current time?",
            "List files in /tmp",
            "Show memory usage"
        ]
        
        # Send messages with small delays
        for i, message in enumerate(messages):
            print(f"  Sending message {i+1}: {message}")
            events = await self.send_message_to_task(task_id, message)
            
            # Check if we got a busy/queued response
            for event in events:
                if event.get('type') == 'status_update':
                    print(f"  ✓ Message queued successfully: {event.get('content', '')}")
                elif event.get('type') == 'error' and 'busy' in event.get('content', '').lower():
                    print(f"  ⚠️  Task busy (old behavior): {event.get('content', '')}")
            
            # Small delay between messages
            await asyncio.sleep(0.5)

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the current status of a task."""
        async with self.session.get(f"{BASE_URL}/task/{task_id}") as response:
            if response.status == 200:
                return await response.json()
            else:
                return {"error": f"Status {response.status}"}

    async def run_tests(self):
        """Run all parallel processing tests."""
        print("🚀 Starting Parallel Processing Tests")
        print("=" * 50)
        
        try:
            # Test 1: Concurrent task creation
            task_ids = await self.test_concurrent_task_creation()
            
            if not task_ids:
                print("❌ No tasks created, cannot continue tests")
                return
            
            # Wait a bit for tasks to initialize
            await asyncio.sleep(2)
            
            # Test 2: Concurrent messaging
            await self.test_concurrent_messaging(task_ids)
            
            # Test 3: Rapid-fire messages to one task
            if task_ids:
                await self.test_rapid_fire_messages(task_ids[0])
            
            # Test 4: Check final status
            print("\n📊 Final task statuses:")
            for task_id in task_ids:
                status = await self.get_task_status(task_id)
                print(f"  Task {task_id}: {status.get('status', 'unknown')}")
                
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()

async def main():
    """Main test function."""
    print("VPS Admin Parallel Processing Test")
    print("Make sure the backend server is running on http://localhost:8000")
    print()
    
    async with ParallelProcessingTester() as tester:
        await tester.run_tests()
    
    print("\n✅ Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
