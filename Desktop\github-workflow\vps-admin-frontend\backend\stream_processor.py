"""
Stream processor module for VPS AI Admin Backend.
Handles main stream processing logic, state machine, and event generation.
"""

import asyncio
import json
import traceback
from typing import AsyncGenerator, Optional

from sse_starlette.sse import ServerSentEvent

from config import Config
from models import SSHResult
from task_manager import TaskManager
from ai_client import AI<PERSON>lient
from ssh_client import SSHClient
from system_scanner import SystemScanner
from orchestrator import TaskOrchestrator


def safe_json_dumps(data, default_error_msg="Error serializing data"):
    """
    Safely serialize data to JSON with error handling.
    Returns a valid JSON string even if serialization fails.
    """
    try:
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
    except (TypeError, ValueError, OverflowError) as e:
        print(f"ERROR: JSON serialization failed: {e}")
        print(f"Problematic data type: {type(data)}")
        print(f"Problematic data (first 500 chars): {str(data)[:500]}")

        # Return a safe error response
        safe_data = {
            "type": "error",
            "content": f"{default_error_msg}: {str(e)}",
            "metadata": {
                "serialization_error": True,
                "original_data_type": str(type(data))
            }
        }
        return json.dumps(safe_data)


class StreamProcessor:
    """Main stream processor for handling user messages and generating SSE events."""

    def __init__(self, task_manager: TaskManager, config: Config):
        self.task_manager = task_manager
        self.config = config
        self.ai_client = AIClient(config)
        self.ssh_client = SSHClient(config)
        self.system_scanner = SystemScanner(self.ssh_client)
        self.orchestrator = TaskOrchestrator(config, self.ssh_client)

    async def process_stream(self, task_id: str, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Main stream processing function."""
        print(f"[Stream {task_id}] INFO: Processing msg: '{user_message[:100]}...'")

        try:
            # Check if task exists
            if not self.task_manager.task_exists(task_id):
                yield ServerSentEvent(
                    data=safe_json_dumps({"type": "error", "content": "Task session not found."}),
                    event="error"
                )
                print(f"[Stream {task_id}] ERROR: Task not found.")
                return

            task = self.task_manager.get_task(task_id)
            status = task.status
            print(f"[Stream {task_id}] DEBUG: Current Status: {status}")

            # LOOP PREVENTION: Check for duplicate messages
            if hasattr(task, 'last_processed_message') and task.last_processed_message == user_message:
                if status in ["PLANNING", "EXECUTING", "REFINING"]:
                    print(f"[Stream {task_id}] WARNING: Duplicate message detected during {status}, ignoring to prevent loops")
                    yield ServerSentEvent(
                        data=safe_json_dumps({
                            "type": "status_update",
                            "content": f"Duplicate request ignored. Task is currently {status.lower()}.",
                            "metadata": {
                                "task_status": status,
                                "duplicate_ignored": True
                            }
                        }),
                        event="message"
                    )
                    return

            # Store the current message to detect duplicates
            task.last_processed_message = user_message

            # Check if task is busy with timeout handling
            if self.task_manager.is_task_busy(task_id):
                # Check if task has been stuck for too long (5 minutes)
                current_time = asyncio.get_event_loop().time()
                time_in_state = current_time - task.created_at

                if time_in_state > 300:  # 5 minutes timeout
                    print(f"[Stream {task_id}] WARNING: Task stuck in PROCESSING_COMMAND for {time_in_state:.1f}s, auto-recovering...")

                    # Auto-recover the stuck task
                    self.task_manager.reset_stuck_task(task_id)

                    yield ServerSentEvent(
                        data=safe_json_dumps({
                            "type": "info",
                            "content": "Task was stuck processing a command and has been automatically recovered. You can now proceed."
                        }),
                        event="message"
                    )

                    # Continue with normal processing
                else:
                    yield ServerSentEvent(
                        data=safe_json_dumps({
                            "type": "error",
                            "content": "Task is currently busy executing a command. Please wait."
                        }),
                        event="error"
                    )
                    print(f"[Stream {task_id}] ERROR: Received message while task was busy ({status}).")
                    return

            # Check if chat session is valid
            if not task.chat:
                yield ServerSentEvent(
                    data=safe_json_dumps({"type": "error", "content": "Task session invalid."}),
                    event="error"
                )
                self.task_manager.update_task_status(task_id, "FAILED")
                print(f"[Stream {task_id}] ERROR: Chat session object missing.")
                return

            # Route to appropriate system based on task configuration
            if self.task_manager.should_use_orchestrator(task_id):
                # Use new Orchestrator system
                print(f"[Stream {task_id}] Using Orchestrator system")
                async for event in self._handle_orchestrator_flow(task_id, user_message):
                    yield event
            else:
                # Use legacy system
                print(f"[Stream {task_id}] Using legacy system")
                # State machine processing
                if status == "AWAITING_USER_CONFIRMATION":
                    async for event in self._handle_confirmation_state(task_id, user_message):
                        yield event
                elif status in ["AWAITING_COMMAND", "AWAITING_USER_INPUT"]:
                    async for event in self._handle_input_state(task_id, user_message):
                        yield event
                else:
                    # Handle terminal or unexpected states
                    async for event in self._handle_terminal_state(task_id, status):
                        yield event

        except Exception as e:
            error_msg = f"Stream processing error: {type(e).__name__}: {e}"
            print(f"[Stream {task_id}] CRITICAL ERROR: {error_msg}\n{traceback.format_exc()}")
            try:
                yield ServerSentEvent(
                    data=safe_json_dumps({"type": "error", "content": error_msg}),
                    event="error"
                )
            except Exception as yield_err:
                print(f"[Stream {task_id}] ERROR: Could not yield final error: {yield_err}")

            self.task_manager.update_task_status(task_id, "FAILED")

        finally:
            task = self.task_manager.get_task(task_id)
            final_status = task.status if task else 'UNKNOWN'
            print(f"[Stream {task_id}] INFO: Finished processing cycle. Final status: {final_status}")

    async def _handle_orchestrator_flow(self, task_id: str, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle message flow using the new Orchestrator system."""
        task = self.task_manager.get_task(task_id)

        # Check if task is in a terminal state
        if task.status in ["COMPLETED", "FAILED", "ABORTED"]:
            yield ServerSentEvent(
                data=safe_json_dumps({
                    "type": "info",
                    "content": f"Task has already finished with status '{task.status}'."
                }),
                event="message"
            )
            return

        # Check if task is awaiting user confirmation
        if task.status == "AWAITING_USER_CONFIRMATION" and task.awaiting_step_confirmation:
            # Handle user confirmation response
            if user_message.lower() in ['yes', 'no']:
                async for event in self.orchestrator.handle_user_confirmation(task, user_message):
                    yield event
            else:
                yield ServerSentEvent(
                    data=safe_json_dumps({
                        "type": "info",
                        "content": "Please respond with 'yes' or 'no' to confirm command execution."
                    }),
                    event="message"
                )
            return

        # Check if task is currently processing with timeout handling
        if task.status in ["PLANNING", "EXECUTING", "REFINING"]:
            # Check if task has been stuck for too long (reduced timeout for PLANNING)
            current_time = asyncio.get_event_loop().time()
            time_in_state = current_time - task.created_at

            # Different timeouts for different states
            timeout_threshold = 300  # 5 minutes default
            if task.status == "PLANNING":
                timeout_threshold = 240  # 4 minutes for planning (AI can be slow)
            elif task.status == "EXECUTING":
                timeout_threshold = 180  # 3 minutes for execution
            elif task.status == "REFINING":
                timeout_threshold = 120  # 2 minutes for refining

            if time_in_state > timeout_threshold:
                print(f"[Stream {task_id}] WARNING: Task stuck in {task.status} for {time_in_state:.1f}s (threshold: {timeout_threshold}s), auto-recovering...")

                # Auto-recover the stuck task
                self.task_manager.reset_stuck_task(task_id)

                yield ServerSentEvent(
                    data=safe_json_dumps({
                        "type": "info",
                        "content": f"Task was stuck in {task.status} state and has been automatically recovered. You can now proceed."
                    }),
                    event="message"
                )

                # Continue with normal processing
            else:
                # LOOP PREVENTION FIX: Block duplicate requests during processing to prevent loops
                print(f"[Stream {task_id}] INFO: Task is busy ({task.status}), checking if request should be allowed...")

                # If task is awaiting confirmation, allow user responses
                if hasattr(task, 'awaiting_step_confirmation') and task.awaiting_step_confirmation:
                    print(f"[Stream {task_id}] INFO: Task awaiting confirmation, processing user response...")
                    # Continue with normal processing to handle user confirmation
                elif user_message.lower().strip() in ['yes', 'y', 'no', 'n', 'cancel', 'abort', 'stop']:
                    print(f"[Stream {task_id}] INFO: Received control command '{user_message}', processing...")
                    # Allow control commands even during processing
                else:
                    # CRITICAL: Block duplicate requests to prevent loops
                    remaining_time = timeout_threshold - time_in_state
                    yield ServerSentEvent(
                        data=safe_json_dumps({
                            "type": "status_update",
                            "content": f"Task is currently {task.status.lower()}. Please wait for the current operation to complete. Time in state: {time_in_state:.1f}s",
                            "metadata": {
                                "task_status": task.status,
                                "time_in_state": time_in_state,
                                "auto_recovery_in": remaining_time,
                                "duplicate_request_blocked": True
                            }
                        }),
                        event="message"
                    )
                    print(f"[Stream {task_id}] INFO: Blocked duplicate request to prevent loops - Status: {task.status}, Time in state: {time_in_state:.1f}s")
                    return

        # Execute the task using the orchestrator
        try:
            async for event in self.orchestrator.execute_task(task, user_message):
                yield event

            # Clear the last processed message when task completes or changes state
            final_task = self.task_manager.get_task(task_id)
            if final_task and final_task.status in ["COMPLETED", "FAILED", "ABORTED"]:
                final_task.last_processed_message = None
                print(f"[Stream {task_id}] INFO: Cleared last processed message for completed task")

        except Exception as e:
            error_msg = f"Orchestrator execution error: {str(e)}"
            print(f"[Stream {task_id}] ERROR: {error_msg}")

            # Clear the last processed message on error
            error_task = self.task_manager.get_task(task_id)
            if error_task:
                error_task.last_processed_message = None

            yield ServerSentEvent(
                data=safe_json_dumps({
                    "type": "error",
                    "content": error_msg
                }),
                event="error"
            )

    async def _handle_confirmation_state(self, task_id: str, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user confirmation state."""
        command_to_run = self.task_manager.get_command_to_confirm(task_id)

        if not command_to_run:
            yield ServerSentEvent(
                data=json.dumps({"type": "error", "content": "State error: No command for confirmation."}),
                event="error"
            )
            self.task_manager.update_task_status(task_id, "FAILED")
            return

        if user_message.strip().lower() == 'yes':
            print(f"[Stream {task_id}] INFO: User confirmed YES: {command_to_run}")

            # Enhanced execution notification
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "info",
                    "content": f"Executing command: {command_to_run}",
                    "metadata": {
                        "category": "command_execution",
                        "priority": "medium"
                    }
                }),
                event="message"
            )

            # Execute command
            async for event in self._execute_command(task_id, command_to_run):
                yield event

        elif user_message.strip().lower() == 'no':
            print(f"[Stream {task_id}] INFO: User cancelled: {command_to_run}")
            yield ServerSentEvent(
                data=json.dumps({"type": "info", "content": "Command execution cancelled."}),
                event="message"
            )
            self.task_manager.update_task_status(task_id, "ABORTED")
            self.task_manager.clear_command_to_confirm(task_id)
            yield ServerSentEvent(
                data=json.dumps({"type": "task_end", "content": "Task aborted by user."}),
                event="end"
            )

        else:
            print(f"[Stream {task_id}] WARNING: Invalid confirm: '{user_message}'.")
            yield ServerSentEvent(
                data=json.dumps({"type": "info", "content": "Please respond 'yes' or 'no'."}),
                event="message"
            )

    async def _handle_input_state(self, task_id: str, user_message: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle user input state."""
        print(f"[Stream {task_id}] DEBUG: Status AWAITING_INPUT. Asking AI based on user input/history.")
        self.task_manager.update_task_status(task_id, "AWAITING_COMMAND")

        task = self.task_manager.get_task(task_id)
        async for event in self._ask_ai_and_process(task_id, task.chat, user_message, task.system_info, None):
            yield event

    async def _handle_terminal_state(self, task_id: str, status: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle terminal or unexpected states."""
        allowed_terminal_states = ["COMPLETED", "FAILED", "ABORTED"]
        if status in allowed_terminal_states:
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "info",
                    "content": f"Task has already finished with status '{status}'."
                }),
                event="message"
            )
            print(f"[Stream {task_id}] INFO: Received message for already finished task ({status}).")
        else:  # Truly unexpected state
            print(f"[Stream {task_id}] WARNING: Unexpected task status '{status}' encountered in main logic.")
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "error",
                    "content": f"Internal error: Unexpected task status '{status}'."
                }),
                event="error"
            )
            self.task_manager.update_task_status(task_id, "FAILED")

    async def _execute_command(self, task_id: str, command: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Execute SSH command and handle results."""
        self.task_manager.update_task_status(task_id, "PROCESSING_COMMAND")
        self.task_manager.clear_command_to_confirm(task_id)

        # Track execution time
        start_time = asyncio.get_event_loop().time()
        print(f"[Stream {task_id}] DEBUG: Status set to PROCESSING_COMMAND. Executing SSH...")

        ssh_result = await self.ssh_client.execute_command_async(command)
        execution_time = int((asyncio.get_event_loop().time() - start_time) * 1000)  # Convert to milliseconds

        print(f"[Stream {task_id}] DEBUG: SSH Result - Success: {ssh_result.success}, Exit: {ssh_result.exit_status}, Time: {execution_time}ms")

        # Record command execution
        self.task_manager.record_command_execution(task_id, ssh_result)

        # Enhanced SSH output with execution metadata
        yield ServerSentEvent(
            data=safe_json_dumps({
                "type": "ssh_output",
                "content": ssh_result.model_dump(),
                "command": command,
                "metadata": {
                    "duration": execution_time,
                    "category": "command_execution",
                    "priority": "low" if ssh_result.success else "medium"
                }
            }),
            event="message"
        )

        # Continue with AI processing
        self.task_manager.update_task_status(task_id, "AWAITING_COMMAND")
        print(f"[Stream {task_id}] DEBUG: Status set to AWAITING_COMMAND. Asking AI for next step (providing outcome).")

        task = self.task_manager.get_task(task_id)
        async for event in self._ask_ai_and_process(task_id, task.chat, "yes", task.system_info, ssh_result):
            yield event

    async def _ask_ai_and_process(self, task_id: str, chat, user_message: str, system_info: str,
                                 last_command_outcome: Optional[SSHResult] = None) -> AsyncGenerator[ServerSentEvent, None]:
        """Enhanced AI helper with better task management, progress tracking, and security awareness."""
        print(f"[Stream {task_id}] INFO: Entering enhanced ask_ai_and_process...")

        task = self.task_manager.get_task(task_id)

        # Enhanced progress tracking
        current_step = task.current_step
        total_steps = task.estimated_steps

        yield ServerSentEvent(
            data=json.dumps({
                "type": "progress",
                "content": "Analyzing request and planning next steps...",
                "metadata": {
                    "stepNumber": current_step,
                    "totalSteps": total_steps,
                    "category": "ai_processing"
                }
            }),
            event="message"
        )

        try:
            # Format history and build prompt with enhanced intent detection
            history_string = self.ai_client.format_chat_history(chat)
            prompt_result = await self.ai_client.build_ai_prompt(
                history_string, user_message, system_info, last_command_outcome
            )

            # Unpack the result
            full_prompt, intent_type, percentage, chat_response = prompt_result

            print(f"[Stream {task_id}] DEBUG: Intent detected: {intent_type}, Percentage: {percentage}%")

            # Handle chat responses directly
            if intent_type == "chat" and chat_response:
                print(f"[Stream {task_id}] DEBUG: Using direct chat response from Gemma")
                next_action = chat_response

                # Update chat history
                self.ai_client.update_chat_history(chat, user_message, next_action)

                # Send chat response
                yield ServerSentEvent(
                    data=json.dumps({
                        "type": "ai_response",
                        "content": next_action,
                        "metadata": {
                            "category": "chat_response",
                            "intent_type": intent_type,
                            "percentage": percentage
                        }
                    }),
                    event="message"
                )

                self.task_manager.update_task_status(task_id, "AWAITING_USER_INPUT")
                return

            # For VPS tasks, use appropriate model based on percentage
            if intent_type == "vps" and percentage >= self.config.MIN_THINKING_PERCENTAGE:
                print(f"[Stream {task_id}] DEBUG: Using Flash 2.5 with thinking for {percentage}% difficulty task")
                next_action = await self.ai_client.generate_response_with_thinking(task_id, full_prompt, percentage)
            else:
                print(f"[Stream {task_id}] DEBUG: Using regular model for {intent_type} intent")
                next_action = await self.ai_client.generate_response(task_id, full_prompt)

            print(f"[Stream {task_id}] DEBUG: AI Response received: '{next_action[:100]}...'")

            # Update chat history
            self.ai_client.update_chat_history(chat, user_message, next_action)

            # Process AI response
            async for event in self._process_ai_response(task_id, next_action):
                yield event

        except Exception as e:
            error_msg = f"AI interaction error: {type(e).__name__}: {e}"
            print(f"[Stream {task_id}] ERROR: {error_msg}\n{traceback.format_exc()}")
            yield ServerSentEvent(
                data=json.dumps({"type": "error", "content": error_msg}),
                event="error"
            )
            self.task_manager.update_task_status(task_id, "FAILED")

    async def _process_ai_response(self, task_id: str, next_action: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Process AI response and generate appropriate events."""
        print(f"[Stream {task_id}] DEBUG: Processing AI response: '{next_action[:100]}...'")

        if not next_action:
            yield ServerSentEvent(
                data=safe_json_dumps({"type": "error", "content": "AI returned empty response."}),
                event="error"
            )
            self.task_manager.update_task_status(task_id, "FAILED")
            return

        task = self.task_manager.get_task(task_id)

        # Update progress
        self.task_manager.increment_step(task_id)
        current_step = task.current_step
        total_steps = task.estimated_steps

        # Check if we've exceeded reasonable step limit
        if current_step > total_steps + 2:  # Allow 2 extra steps beyond estimate
            print(f"[Stream {task_id}] WARNING: Task exceeded step limit ({current_step}/{total_steps}), forcing completion.")
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "progress",
                    "content": "Task has taken longer than expected. Completing now.",
                    "metadata": {
                        "stepNumber": total_steps,
                        "totalSteps": total_steps,
                        "category": "task_completion"
                    }
                }),
                event="message"
            )

            self.task_manager.update_task_status(task_id, "COMPLETED")
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "task_end",
                    "content": "Task completed (exceeded step limit).",
                    "metadata": {"category": "task_management"}
                }),
                event="end"
            )
            return

        # Handle different response types
        if next_action == "TASK_COMPLETE":
            async for event in self._handle_task_completion(task_id, task):
                yield event
        elif next_action.startswith("QUESTION:"):
            async for event in self._handle_ai_question(task_id, next_action):
                yield event
        elif next_action.startswith("SECURITY_ALERT:"):
            async for event in self._handle_security_alert(task_id, next_action):
                yield event
        else:
            # Check if it's a command or regular text response
            async for event in self._handle_command_or_text(task_id, next_action):
                yield event

    async def _handle_task_completion(self, task_id: str, task) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle task completion."""
        yield ServerSentEvent(
            data=json.dumps({
                "type": "progress",
                "content": "Task completed successfully!",
                "metadata": {
                    "stepNumber": task.current_step,
                    "totalSteps": task.estimated_steps,
                    "category": "task_completion"
                }
            }),
            event="message"
        )

        self.task_manager.update_task_status(task_id, "COMPLETED")
        print(f"[Stream {task_id}] INFO: Enhanced task complete.")

        # Generate enhanced summary
        try:
            summary_text = await self.ai_client.generate_summary(task.chat)
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "summary",
                    "content": summary_text,
                    "metadata": {"category": "task_summary"}
                }),
                event="message"
            )
        except Exception as e:
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "warning",
                    "content": f"Could not generate summary: {e}",
                    "metadata": {"category": "summary_error"}
                }),
                event="message"
            )

        yield ServerSentEvent(
            data=json.dumps({
                "type": "task_end",
                "content": "Enhanced task completed successfully.",
                "metadata": {"category": "task_management"}
            }),
            event="end"
        )

    async def _handle_ai_question(self, task_id: str, next_action: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle AI question."""
        question = next_action[len("QUESTION:"):].strip()
        yield ServerSentEvent(
            data=json.dumps({
                "type": "question",
                "content": question,
                "metadata": {
                    "category": "user_interaction",
                    "priority": "medium"
                }
            }),
            event="message"
        )
        self.task_manager.update_task_status(task_id, "AWAITING_USER_INPUT")
        print(f"[Stream {task_id}] INFO: AI asked enhanced question.")

    async def _handle_security_alert(self, task_id: str, next_action: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle security alert."""
        alert = next_action[len("SECURITY_ALERT:"):].strip()
        yield ServerSentEvent(
            data=json.dumps({
                "type": "security_alert",
                "content": alert,
                "metadata": {
                    "category": "security",
                    "priority": "critical"
                }
            }),
            event="message"
        )
        self.task_manager.update_task_status(task_id, "AWAITING_USER_INPUT")
        print(f"[Stream {task_id}] WARNING: Security alert raised.")

    async def _handle_command_or_text(self, task_id: str, response_text: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle command or text response."""
        response_text = response_text.strip()

        # Enhanced command detection
        command_indicators = [
            'sudo ', 'apt ', 'yum ', 'systemctl ', 'service ', 'chmod ', 'chown ', 'cp ', 'mv ', 'rm ',
            'ls ', 'cd ', 'pwd ', 'ps ', 'kill ', 'top ', 'htop ', 'df ', 'du ', 'free ', 'uname ',
            'wget ', 'curl ', 'ssh ', 'scp ', 'rsync ', 'tar ', 'zip ', 'unzip ', 'grep ', 'find ',
            'awk ', 'sed ', 'cat ', 'head ', 'tail ', 'less ', 'more ', 'nano ', 'vim ', 'vi ',
            'git ', 'docker ', 'npm ', 'node ', 'python ', 'pip ', 'make ', 'cmake ', 'gcc ',
            'java ', 'javac ', 'mvn ', 'gradle ', 'composer ', 'php ', 'ruby ', 'gem ', 'bundle ',
            'crontab ', 'mount ', 'umount ', 'fdisk ', 'lsblk ', 'netstat ', 'ss ', 'iptables '
        ]

        # Check if response looks like a command
        is_command = any(response_text.lower().startswith(cmd) for cmd in command_indicators)
        is_command = is_command or response_text.startswith('./') or any(char in response_text for char in ['|', '>', '<', '&&', '||'])

        if is_command:
            async for event in self._handle_command_response(task_id, response_text):
                yield event
        else:
            # Treat as regular AI text response
            yield ServerSentEvent(
                data=safe_json_dumps({
                    "type": "ai_response",
                    "content": response_text,
                    "metadata": {
                        "category": "ai_response",
                        "stepNumber": self.task_manager.get_task(task_id).current_step,
                        "totalSteps": self.task_manager.get_task(task_id).estimated_steps
                    }
                }),
                event="message"
            )
            self.task_manager.update_task_status(task_id, "AWAITING_USER_INPUT")
            print(f"[Stream {task_id}] INFO: AI provided text response, awaiting user input.")

    async def _handle_command_response(self, task_id: str, command_to_confirm: str) -> AsyncGenerator[ServerSentEvent, None]:
        """Handle command response with security analysis."""
        # Check for command repetition to prevent loops
        recent_commands = self.task_manager.get_recent_commands(task_id)
        if command_to_confirm in recent_commands[-3:]:  # Check last 3 commands
            yield ServerSentEvent(
                data=json.dumps({
                    "type": "ai_response",
                    "content": f"I notice I'm repeating the same command. Let me try a different approach or ask for clarification. The command `{command_to_confirm}` was recently attempted. Would you like me to:\n\n1. Try a different approach\n2. Check the current status\n3. Move to the next step\n\nPlease let me know how you'd like to proceed.",
                    "metadata": {
                        "category": "ai_response",
                        "stepNumber": self.task_manager.get_task(task_id).current_step,
                        "totalSteps": self.task_manager.get_task(task_id).estimated_steps
                    }
                }),
                event="message"
            )
            self.task_manager.update_task_status(task_id, "AWAITING_USER_INPUT")
            print(f"[Stream {task_id}] WARNING: AI attempted to repeat command, asking for guidance.")
            return

        # Basic security analysis
        dangerous_patterns = [
            'rm -rf /', 'chmod 777', 'chown -R root', 'iptables -F',
            'ufw disable', 'systemctl stop ssh', 'passwd -d', 'sudo su -'
        ]

        security_risk = any(pattern in command_to_confirm.lower() for pattern in dangerous_patterns)

        # Determine command priority and category
        priority = "critical" if security_risk else "medium"
        category = "system_security" if security_risk else "system_administration"

        if 'install' in command_to_confirm.lower():
            category = "package_management"
        elif any(service_cmd in command_to_confirm.lower() for service_cmd in ['systemctl', 'service']):
            category = "service_management"
        elif any(file_cmd in command_to_confirm.lower() for file_cmd in ['cp', 'mv', 'rm', 'chmod', 'chown']):
            category = "file_management"

        # Track recent commands
        self.task_manager.add_recent_command(task_id, command_to_confirm)

        yield ServerSentEvent(
            data=json.dumps({
                "type": "command_confirmation",
                "content": command_to_confirm,
                "metadata": {
                    "category": category,
                    "priority": priority,
                    "stepNumber": self.task_manager.get_task(task_id).current_step,
                    "totalSteps": self.task_manager.get_task(task_id).estimated_steps,
                    "securityRisk": security_risk
                }
            }),
            event="message"
        )

        self.task_manager.update_task_status(task_id, "AWAITING_USER_CONFIRMATION")
        self.task_manager.set_command_to_confirm(task_id, command_to_confirm)
        print(f"[Stream {task_id}] INFO: AI proposed enhanced command with security analysis.")
