/**
 * Stream handling hook for VPS Admin Chat
 * Uses fetchEventSource for POST request support
 */

import { useCallback, useRef } from 'react';
import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';
import { UseStreamHandlerReturn, SSEEventData } from '../types';
import { API_BASE_URL } from '../constants';
import { safeJsonParse, debugJsonString } from '../utils';

export const useStreamHandler = (
  onMessage: (data: SSEEventData) => void,
  onError: (error: Error) => void,
  onClose: () => void
): UseStreamHandlerReturn => {
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleStream = useCallback((taskId: string, message: string) => {
    // Abort any existing stream to prevent conflicts
    if (abortControllerRef.current) {
      console.log("DEBUG: Aborting previous stream for new request");
      abortStream();
    }

    // Create new abort controller
    const controller = new AbortController();
    abortControllerRef.current = controller;

    const url = `${API_BASE_URL}/send_message`;
    const payload = { task_id: taskId, message: message };

    console.log(`DEBUG: Starting fetchEventSource to ${url} for task ${taskId}`);

    fetchEventSource(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Accept': 'text/event-stream' },
      body: JSON.stringify(payload),
      signal: controller.signal,

      // Called when the connection is initially established
      async onopen(response: Response) {
        console.log("DEBUG: fetchEventSource onopen triggered.");
        if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
          console.log("INFO: SSE connection established successfully.");
        } else {
          // Handle non-SSE responses or errors during connection setup
          response.text().then(bodyText => {
            const errorDetail = `Failed to open SSE connection: Status ${response.status}. Check backend server and logs. Body: ${bodyText.substring(0, 500)}`;
            console.error(errorDetail);
            onError(new Error(errorDetail));
            controller.abort();
            abortControllerRef.current = null;
          }).catch(err => {
            const errorDetail = `Failed to open SSE connection: Status ${response.status}. Failed to read error body: ${err}`;
            console.error(errorDetail);
            onError(new Error(errorDetail));
            controller.abort();
            abortControllerRef.current = null;
          });
        }
      },

      // Called for each message received from the server
      onmessage(event: EventSourceMessage) {
        console.log("DEBUG: fetchEventSource onmessage:", event.data);

        // Enhanced error handling for JSON parsing
        try {
          // Check if event.data is empty or null
          if (!event.data || event.data.trim() === '') {
            console.warn("Received empty SSE data, skipping...");
            return;
          }

          // Use safe JSON parsing utility
          const parseResult = safeJsonParse(event.data);

          if (!parseResult.success) {
            console.error("=== JSON PARSING ERROR ===");
            debugJsonString(event.data);
            console.error("Parse error:", parseResult.error);
            console.error("========================");

            // Provide specific error message based on the issue
            let errorMessage = 'Failed to parse response from server.';

            if (parseResult.error?.includes('Unexpected token')) {
              errorMessage = `Server sent malformed JSON: ${parseResult.error}`;
            } else if (event.data?.includes('<html>') || event.data?.includes('<!DOCTYPE')) {
              errorMessage = 'Server sent HTML instead of JSON. Check if backend is running correctly.';
            } else if (event.data?.length === 0) {
              errorMessage = 'Server sent empty response.';
            } else if (event.data?.length > 10000) {
              errorMessage = 'Server sent unusually large response that could not be parsed.';
            } else {
              errorMessage = `JSON parsing failed: ${parseResult.error}`;
            }

            onError(new Error(errorMessage));
            return;
          }

          const data = parseResult.data;

          // Validate that the parsed data has expected structure
          if (typeof data !== 'object' || data === null) {
            console.warn("Parsed data is not an object:", data);
            onError(new Error(`Invalid response format: Expected object, got ${typeof data}`));
            return;
          }

          // Check for required fields
          if (!data.type) {
            console.warn("Parsed data missing 'type' field:", data);
            onError(new Error('Invalid response format: Missing message type'));
            return;
          }

          onMessage(data);
        } catch (unexpectedError) {
          // This should rarely happen now with safe parsing
          console.error("=== UNEXPECTED ERROR ===");
          console.error("Unexpected error:", unexpectedError);
          debugJsonString(event.data);
          console.error("========================");

          onError(new Error(`Unexpected error processing server response: ${unexpectedError instanceof Error ? unexpectedError.message : 'Unknown error'}`));
        }
      },

      // Called when the connection is closed
      onclose() {
        console.log("DEBUG: fetchEventSource onclose triggered.");
        abortControllerRef.current = null;
        onClose();
      },

      // Called when an error occurs
      onerror(err: any) {
        console.error("DEBUG: fetchEventSource onerror triggered:", err);

        // Check if the error was due to an intentional abort
        if (controller.signal.aborted) {
          console.log("Stream aborted intentionally.");
          return;
        }

        // Filter out common abort-related errors that are not user-facing issues
        const errorMsg = err.message || 'Connection error with the backend. Check server status.';

        // Don't show BodyStreamBuffer abort errors to users - these are expected during stream switching
        if (errorMsg.includes('BodyStreamBuffer was aborted') ||
            errorMsg.includes('The operation was aborted') ||
            errorMsg.includes('AbortError')) {
          console.log("Stream was aborted (expected behavior during request switching)");
          return;
        }

        // Process as a genuine connection error
        console.error("fetchEventSource Error:", errorMsg);
        onError(new Error(errorMsg));
        abortControllerRef.current = null;
      }
    });
  }, [onMessage, onError, onClose]);

  const abortStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const isStreamActive = useCallback(() => {
    return abortControllerRef.current !== null;
  }, []);

  const getStreamState = useCallback(() => {
    return abortControllerRef.current ? 'ACTIVE' : 'INACTIVE';
  }, []);

  return {
    handleStream,
    abortStream,
    isStreamActive,
    getStreamState
  };
};
