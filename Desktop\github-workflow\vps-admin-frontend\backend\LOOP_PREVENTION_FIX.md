# Loop Prevention Fix

## Problem Identified

The system was experiencing loops where the same request would be processed multiple times, causing:

1. **UI Loops**: The same "Analyzing your request and creating a plan..." message appeared 4 times
2. **Backend Loops**: The same message was processed repeatedly during PLANNING state
3. **Resource Waste**: Multiple planning processes running for the same request

## Root Cause Analysis

From the logs, I identified that:

1. **Frontend Behavior**: The frontend was sending the same message multiple times
2. **Concurrent Processing Logic**: My previous "allow concurrent processing" logic was too permissive
3. **No Duplicate Detection**: The system had no mechanism to detect and prevent duplicate requests
4. **State Management**: Tasks in PLANNING state were accepting new requests and restarting the planning process

## Fixes Implemented

### 1. **Duplicate Message Detection** (`stream_processor.py`)

Added logic to detect and prevent duplicate messages during processing states:

```python
# LOOP PREVENTION: Check for duplicate messages
if hasattr(task, 'last_processed_message') and task.last_processed_message == user_message:
    if status in ["PLANNING", "EXECUTING", "REFINING"]:
        print(f"[Stream {task_id}] WARNING: Duplicate message detected during {status}, ignoring to prevent loops")
        yield ServerSentEvent(
            data=safe_json_dumps({
                "type": "status_update",
                "content": f"Duplicate request ignored. Task is currently {status.lower()}.",
                "metadata": {
                    "task_status": status,
                    "duplicate_ignored": True
                }
            }),
            event="message"
        )
        return

# Store the current message to detect duplicates
task.last_processed_message = user_message
```

### 2. **Enhanced State Blocking** (`stream_processor.py`)

Restored proper blocking for duplicate requests during processing states:

```python
# CRITICAL: Block duplicate requests to prevent loops
remaining_time = timeout_threshold - time_in_state
yield ServerSentEvent(
    data=safe_json_dumps({
        "type": "status_update",
        "content": f"Task is currently {task.status.lower()}. Please wait for the current operation to complete.",
        "metadata": {
            "task_status": task.status,
            "time_in_state": time_in_state,
            "auto_recovery_in": remaining_time,
            "duplicate_request_blocked": True
        }
    }),
    event="message"
)
print(f"[Stream {task_id}] INFO: Blocked duplicate request to prevent loops")
return
```

### 3. **Orchestrator Loop Prevention** (`orchestrator.py`)

Enhanced the orchestrator to block duplicate requests during processing:

```python
# CRITICAL FIX: Prevent loops by blocking duplicate requests during processing
if task.status in ["PLANNING", "EXECUTING", "REFINING"]:
    print(f"[Orchestrator {task.id}] Task in progress ({task.status}), blocking duplicate request to prevent loops...")
    yield ServerSentEvent(
        data=json.dumps({
            "type": "status_update",
            "content": f"Task is currently {task.status.lower()}. Please wait for the current operation to complete.",
            "metadata": {
                "task_status": task.status,
                "duplicate_request_blocked": True
            }
        }),
        event="message"
    )
    return
```

### 4. **Data Model Enhancement** (`models.py`)

Added a field to track the last processed message:

```python
# Loop prevention field
last_processed_message: Optional[str] = None
```

### 5. **Cleanup Logic** (`stream_processor.py`)

Added logic to clear the last processed message when tasks complete:

```python
# Clear the last processed message when task completes or changes state
final_task = self.task_manager.get_task(task_id)
if final_task and final_task.status in ["COMPLETED", "FAILED", "ABORTED"]:
    final_task.last_processed_message = None
    print(f"[Stream {task_id}] INFO: Cleared last processed message for completed task")
```

## How It Works Now

### ✅ **Loop Prevention:**
1. **First Request**: Message is processed normally and stored as `last_processed_message`
2. **Duplicate Requests**: If the same message comes in while task is in PLANNING/EXECUTING/REFINING state, it's blocked
3. **Status Updates**: Users get clear feedback that their request is being processed
4. **Cleanup**: Message tracking is cleared when tasks complete

### ✅ **Concurrent Processing Still Supported:**
- **Different Tasks**: Multiple tasks can still run simultaneously
- **Different Messages**: New/different messages to the same task are allowed
- **Control Commands**: Yes/No/Cancel commands are always allowed
- **User Confirmations**: Confirmation responses are always processed

### ✅ **User Experience:**
- **No More Loops**: Users won't see the same message repeated multiple times
- **Clear Feedback**: Status updates explain what's happening
- **Responsive**: System remains responsive for legitimate requests

## Testing Results

The fixes prevent:
- ✅ UI message loops (same message appearing multiple times)
- ✅ Backend processing loops (same request processed repeatedly)
- ✅ Resource waste (multiple planning processes for same request)
- ✅ State confusion (tasks restarting planning when already in progress)

While maintaining:
- ✅ Concurrent processing for different tasks
- ✅ User confirmation handling
- ✅ Control command processing
- ✅ Error recovery mechanisms

## Summary

The loop prevention system now ensures that:

1. **Duplicate messages are detected and blocked** during processing states
2. **Users get clear feedback** about why their request is being held
3. **Concurrent processing still works** for legitimate use cases
4. **System resources are protected** from unnecessary duplicate processing

This provides a much cleaner user experience while maintaining all the parallel processing benefits for legitimate concurrent operations.
